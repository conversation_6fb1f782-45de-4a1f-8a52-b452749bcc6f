<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vulnerability Management Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .dashboard-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .metric { text-align: center; padding: 15px; }
        .metric h3 { font-size: 2em; margin-bottom: 5px; }
        .critical { color: #d32f2f; }
        .high { color: #f57c00; }
        .medium { color: #7b1fa2; }
        .low { color: #388e3c; }
        .status-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .status-table th, .status-table td { padding: 10px; text-align: left; border-bottom: 1px solid #eee; }
        .status-table th { background-color: #f8f9fa; font-weight: 600; }
        .status-indicator { width: 12px; height: 12px; border-radius: 50%; display: inline-block; margin-right: 8px; }
        .status-compliant { background-color: #4caf50; }
        .status-non-compliant { background-color: #f44336; }
        .status-unknown { background-color: #9e9e9e; }
        .refresh-btn { background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px; }
        .refresh-btn:hover { background: #5a6fd8; }
        .chart-container { position: relative; height: 300px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Vulnerability Management Dashboard</h1>
        <p>Real-time security posture monitoring</p>
        <button class="refresh-btn" onclick="refreshData()">🔄 Refresh Data</button>
    </div>

    <div class="container">
        <!-- Summary Metrics -->
        <div class="dashboard-grid">
            <div class="card">
                <div class="metric critical">
                    <h3 id="critical-count">-</h3>
                    <p>Critical Vulnerabilities</p>
                </div>
            </div>
            <div class="card">
                <div class="metric high">
                    <h3 id="high-count">-</h3>
                    <p>High Priority</p>
                </div>
            </div>
            <div class="card">
                <div class="metric medium">
                    <h3 id="medium-count">-</h3>
                    <p>Medium Priority</p>
                </div>
            </div>
            <div class="card">
                <div class="metric low">
                    <h3 id="patch-compliance">-</h3>
                    <p>Patch Compliance %</p>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="dashboard-grid">
            <div class="card">
                <h3>Vulnerability Trends</h3>
                <div class="chart-container">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
            <div class="card">
                <h3>Platform Distribution</h3>
                <div class="chart-container">
                    <canvas id="platformChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Device Status Table -->
        <div class="card">
            <h3>Device Status Overview</h3>
            <table class="status-table" id="deviceTable">
                <thead>
                    <tr>
                        <th>Device Name</th>
                        <th>Platform</th>
                        <th>Compliance Status</th>
                        <th>Critical Vulns</th>
                        <th>Last Updated</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="deviceTableBody">
                    <!-- Data will be populated by JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- Recent Activities -->
        <div class="card">
            <h3>Recent Patch Activities</h3>
            <div id="recentActivities">
                <!-- Activities will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // Sample data - replace with actual API calls
        let dashboardData = {
            summary: {
                critical: 12,
                high: 28,
                medium: 45,
                patchCompliance: 78
            },
            devices: [
                { name: "WS-001", platform: "Windows 11", compliance: "Compliant", criticalVulns: 0, lastUpdate: "2025-08-12 10:30" },
                { name: "SRV-002", platform: "Ubuntu 22.04", compliance: "Non-Compliant", criticalVulns: 3, lastUpdate: "2025-08-11 14:20" },
                { name: "WS-003", platform: "Windows 10", compliance: "Non-Compliant", criticalVulns: 1, lastUpdate: "2025-08-10 09:15" }
            ],
            trends: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                critical: [15, 12, 18, 8, 10, 12],
                high: [32, 28, 35, 25, 30, 28]
            }
        };

        // Initialize dashboard
        function initDashboard() {
            updateSummaryMetrics();
            updateDeviceTable();
            createCharts();
            updateRecentActivities();
        }

        function updateSummaryMetrics() {
            document.getElementById('critical-count').textContent = dashboardData.summary.critical;
            document.getElementById('high-count').textContent = dashboardData.summary.high;
            document.getElementById('medium-count').textContent = dashboardData.summary.medium;
            document.getElementById('patch-compliance').textContent = dashboardData.summary.patchCompliance + '%';
        }

        function updateDeviceTable() {
            const tbody = document.getElementById('deviceTableBody');
            tbody.innerHTML = '';

            dashboardData.devices.forEach(device => {
                const row = tbody.insertRow();
                const statusClass = device.compliance === 'Compliant' ? 'status-compliant' : 'status-non-compliant';

                row.innerHTML = `
                    <td>${device.name}</td>
                    <td>${device.platform}</td>
                    <td><span class="status-indicator ${statusClass}"></span>${device.compliance}</td>
                    <td>${device.criticalVulns}</td>
                    <td>${device.lastUpdate}</td>
                    <td><button onclick="triggerPatch('${device.name}')" class="refresh-btn">Patch Now</button></td>
                `;
            });
        }

        function createCharts() {
            // Trend Chart
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: dashboardData.trends.labels,
                    datasets: [{
                        label: 'Critical',
                        data: dashboardData.trends.critical,
                        borderColor: '#d32f2f',
                        backgroundColor: 'rgba(211, 47, 47, 0.1)'
                    }, {
                        label: 'High',
                        data: dashboardData.trends.high,
                        borderColor: '#f57c00',
                        backgroundColor: 'rgba(245, 124, 0, 0.1)'
                    }]
                },
                options: { responsive: true, maintainAspectRatio: false }
            });

            // Platform Chart
            const platformCtx = document.getElementById('platformChart').getContext('2d');
            new Chart(platformCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Windows', 'Linux', 'Cloud'],
                    datasets: [{
                        data: [60, 30, 10],
                        backgroundColor: ['#4285f4', '#34a853', '#fbbc05']
                    }]
                },
                options: { responsive: true, maintainAspectRatio: false }
            });
        }

        function updateRecentActivities() {
            const activities = [
                { time: '10:30 AM', action: 'Patch deployed to Pilot Ring', status: 'success' },
                { time: '09:15 AM', action: 'Critical CVE-2025-0001 detected', status: 'warning' },
                { time: '08:45 AM', action: 'Weekly vulnerability scan completed', status: 'info' }
            ];

            const container = document.getElementById('recentActivities');
            container.innerHTML = activities.map(activity =>
                `<div style="padding: 10px; border-left: 3px solid ${getStatusColor(activity.status)}; margin: 5px 0; background: #f9f9f9;">
                    <strong>${activity.time}</strong> - ${activity.action}
                </div>`
            ).join('');
        }

        function getStatusColor(status) {
            const colors = { success: '#4caf50', warning: '#ff9800', info: '#2196f3' };
            return colors[status] || '#9e9e9e';
        }

        function triggerPatch(deviceName) {
            if (confirm(`Trigger patch deployment for ${deviceName}?`)) {
                alert(`Patch deployment initiated for ${deviceName}`);
                // Here you would call your patch deployment API
            }
        }

        function refreshData() {
            // In a real implementation, this would fetch fresh data from your APIs
            console.log('Refreshing dashboard data...');
            // You could call your PowerShell scripts or APIs here
        }

        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', initDashboard);

        // Auto-refresh every 5 minutes
        setInterval(refreshData, 300000);
    </script>
</body>
</html>