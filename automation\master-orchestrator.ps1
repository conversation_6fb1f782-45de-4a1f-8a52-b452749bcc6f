# Master Vulnerability Management Orchestrator
# Coordinates patch management across Windows (Intune), Linux, and Cloud environments

param(
    [Parameter(Mandatory=$true)]
    [string]$TenantId,

    [Parameter(Mandatory=$false)]
    [string]$ConfigFile = ".\config\automation-config.json",

    [Parameter(Mandatory=$false)]
    [switch]$DryRun,

    [Parameter(Mandatory=$false)]
    [switch]$GenerateReportsOnly
)

# Import configuration
if (Test-Path $ConfigFile) {
    $config = Get-Content $ConfigFile | ConvertFrom-Json
} else {
    Write-Error "Configuration file not found: $ConfigFile"
    exit 1
}

# Function to execute Windows patch management via Intune
function Invoke-WindowsPatchManagement {
    Write-Host "=== Windows Patch Management (Intune) ===" -ForegroundColor Cyan

    if ($DryRun) {
        Write-Host "DRY RUN: Would execute Intune patch policies" -ForegroundColor Yellow
        return @{ Success = $true; Message = "Dry run completed" }
    }

    try {
        # Execute Intune patch policy script
        $intuneResult = & ".\intune-patch-policies.ps1" -TenantId $TenantId

        Write-Host "Intune patch management completed successfully" -ForegroundColor Green
        return @{ Success = $true; Result = $intuneResult }
    }
    catch {
        Write-Error "Intune patch management failed: $($_.Exception.Message)"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Function to execute Linux patch management
function Invoke-LinuxPatchManagement {
    Write-Host "=== Linux Server Patch Management ===" -ForegroundColor Cyan

    $linuxServers = $config.LinuxServers
    $results = @()

    foreach ($server in $linuxServers) {
        Write-Host "Processing Linux server: $($server.Hostname)" -ForegroundColor Yellow

        if ($DryRun) {
            Write-Host "DRY RUN: Would patch $($server.Hostname)" -ForegroundColor Yellow
            $results += @{ Server = $server.Hostname; Status = "Dry Run" }
            continue
        }

        try {
            # Execute remote patch management
            $scriptBlock = {
                python3 /opt/patch-automation/linux-patch-automation.py
            }

            if ($server.UseSSH) {
                # SSH execution for remote Linux servers
                $sshResult = ssh $server.Username@$server.Hostname $scriptBlock
                $results += @{ Server = $server.Hostname; Status = "Completed"; Output = $sshResult }
            } else {
                # Local execution or other connection method
                $localResult = Invoke-Command -ScriptBlock $scriptBlock
                $results += @{ Server = $server.Hostname; Status = "Completed"; Output = $localResult }
            }

            Write-Host "Linux server $($server.Hostname) patched successfully" -ForegroundColor Green
        }
        catch {
            Write-Error "Failed to patch Linux server $($server.Hostname): $($_.Exception.Message)"
            $results += @{ Server = $server.Hostname; Status = "Failed"; Error = $_.Exception.Message }
        }
    }

    return $results
}

# Function to execute cloud resource patch management
function Invoke-CloudPatchManagement {
    Write-Host "=== Cloud Resource Patch Management ===" -ForegroundColor Cyan

    if ($DryRun) {
        Write-Host "DRY RUN: Would execute cloud patch management" -ForegroundColor Yellow
        return @{ Success = $true; Message = "Dry run completed" }
    }

    $cloudResults = @()

    # Azure Update Management
    if ($config.Azure.Enabled) {
        try {
            Write-Host "Executing Azure Update Management..." -ForegroundColor Yellow

            # Connect to Azure
            Connect-AzAccount -TenantId $TenantId

            # Trigger update deployments
            foreach ($deployment in $config.Azure.UpdateDeployments) {
                $result = Start-AzAutomationSoftwareUpdateConfiguration -ResourceGroupName $deployment.ResourceGroup -AutomationAccountName $deployment.AutomationAccount -SoftwareUpdateConfigurationName $deployment.ConfigurationName

                $cloudResults += @{
                    Platform = "Azure"
                    Deployment = $deployment.ConfigurationName
                    Status = "Triggered"
                    JobId = $result.JobId
                }
            }

            Write-Host "Azure patch management initiated" -ForegroundColor Green
        }
        catch {
            Write-Error "Azure patch management failed: $($_.Exception.Message)"
            $cloudResults += @{ Platform = "Azure"; Status = "Failed"; Error = $_.Exception.Message }
        }
    }

    return $cloudResults
}

# Function to generate comprehensive report
function New-ComprehensiveReport {
    param(
        [hashtable]$WindowsResults,
        [array]$LinuxResults,
        [array]$CloudResults
    )

    Write-Host "=== Generating Comprehensive Report ===" -ForegroundColor Cyan

    $reportData = @{
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Summary = @{
            WindowsDevices = @{
                Status = $WindowsResults.Success
                Message = if ($WindowsResults.Success) { "Completed" } else { $WindowsResults.Error }
            }
            LinuxServers = @{
                Total = $LinuxResults.Count
                Successful = ($LinuxResults | Where-Object { $_.Status -eq "Completed" }).Count
                Failed = ($LinuxResults | Where-Object { $_.Status -eq "Failed" }).Count
            }
            CloudResources = @{
                Total = $CloudResults.Count
                Triggered = ($CloudResults | Where-Object { $_.Status -eq "Triggered" }).Count
                Failed = ($CloudResults | Where-Object { $_.Status -eq "Failed" }).Count
            }
        }
        Details = @{
            Windows = $WindowsResults
            Linux = $LinuxResults
            Cloud = $CloudResults
        }
    }

    # Save JSON report
    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $jsonReportPath = ".\reports\comprehensive-report-$timestamp.json"
    $reportData | ConvertTo-Json -Depth 10 | Out-File -FilePath $jsonReportPath -Encoding UTF8

    Write-Host "Reports generated:" -ForegroundColor Green
    Write-Host "  JSON: $jsonReportPath" -ForegroundColor White

    return $reportData
}

# Main execution
Write-Host "Starting Master Vulnerability Management Orchestrator..." -ForegroundColor Yellow

# Create reports directory
if (!(Test-Path ".\reports")) {
    New-Item -Path ".\reports" -ItemType Directory -Force | Out-Null
}

# Execute patch management workflows
$windowsResults = Invoke-WindowsPatchManagement
$linuxResults = Invoke-LinuxPatchManagement
$cloudResults = Invoke-CloudPatchManagement

# Generate comprehensive report
$finalReport = New-ComprehensiveReport -WindowsResults $windowsResults -LinuxResults $linuxResults -CloudResults $cloudResults

Write-Host "Master orchestration completed!" -ForegroundColor Green