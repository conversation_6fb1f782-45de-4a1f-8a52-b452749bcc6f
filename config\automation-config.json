{"General": {"OrganizationName": "Your Organization", "MaintenanceWindow": "02:00-04:00", "NotificationEmail": "<EMAIL>", "ReportRetentionDays": 90}, "Intune": {"Enabled": true, "TenantId": "your-tenant-id-here", "DeploymentRings": {"Pilot": {"DeferQualityUpdatesDays": 0, "DeferFeatureUpdatesDays": 0, "MaintenanceWindow": "02:00"}, "Broad": {"DeferQualityUpdatesDays": 7, "DeferFeatureUpdatesDays": 30, "MaintenanceWindow": "02:00"}, "Critical": {"DeferQualityUpdatesDays": 14, "DeferFeatureUpdatesDays": 60, "MaintenanceWindow": "03:00"}}}, "LinuxServers": [{"Hostname": "linux-server-01.domain.com", "Username": "admin", "UseSSH": true, "Distribution": "ubuntu", "Environment": "production", "MaintenanceWindow": "02:00-04:00", "AutoReboot": false}, {"Hostname": "linux-server-02.domain.com", "Username": "admin", "UseSSH": true, "Distribution": "rhel", "Environment": "production", "MaintenanceWindow": "03:00-05:00", "AutoReboot": false}], "Azure": {"Enabled": true, "SubscriptionId": "your-subscription-id", "UpdateDeployments": [{"ResourceGroup": "rg-automation", "AutomationAccount": "automation-account-01", "ConfigurationName": "SecurityUpdates-Production"}]}, "AWS": {"Enabled": false, "Region": "us-east-1", "PatchGroups": [{"Name": "Production-Windows", "MaintenanceWindow": "mw-prod-windows"}, {"Name": "Production-Linux", "MaintenanceWindow": "mw-prod-linux"}]}, "Notifications": {"Webhook": {"Enabled": true, "Url": "https://your-webhook-url.com/vulnerability-reports", "Headers": {"Authorization": "Bearer your-token-here"}}, "Email": {"Enabled": true, "SmtpServer": "smtp.office365.com", "Port": 587, "UseTLS": true, "From": "<EMAIL>", "Recipients": ["<EMAIL>", "<EMAIL>"]}, "Teams": {"Enabled": true, "WebhookUrl": "https://outlook.office.com/webhook/your-teams-webhook"}}, "Scheduling": {"VulnerabilityScans": {"Frequency": "Daily", "Time": "01:00"}, "PatchDeployment": {"Frequency": "Weekly", "Day": "Sunday", "Time": "02:00"}, "Reporting": {"Frequency": "Daily", "Time": "08:00", "WeeklyReport": {"Day": "Monday", "Time": "09:00"}}}, "RiskManagement": {"CriticalVulnerabilityThreshold": 9.0, "HighVulnerabilityThreshold": 7.0, "PatchComplianceTarget": 95, "MaxDowntimeMinutes": 30, "RollbackEnabled": true}}