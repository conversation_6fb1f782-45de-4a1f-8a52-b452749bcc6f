# Vulnerability Management Automation Suite

A comprehensive automation solution for managing vulnerabilities across Windows (Intune), Linux, and Cloud environments.

## 🚀 Quick Start

### 1. Initial Setup
```powershell
# Run the setup script to install dependencies and configure the environment
.\setup-automation.ps1 -InstallModules -ConfigureScheduledTasks -TenantId "your-tenant-id"
```

### 2. Configuration
Edit `config/automation-config.json` with your environment details:
- Update TenantId, server hostnames, and notification settings
- Configure maintenance windows and deployment rings
- Set up cloud provider credentials

### 3. Test Run
```powershell
# Test the automation with a dry run
.\automation\master-orchestrator.ps1 -TenantId "your-tenant-id" -DryRun
```

### 4. Monitor
Open `automation/vulnerability-dashboard.html` in your browser for real-time monitoring.

## 📁 Project Structure

```
├── automation/
│   ├── intune-patch-policies.ps1      # Intune Windows patch management
│   ├── vulnerability-assessment.ps1    # Vulnerability scanning and reporting
│   ├── linux-patch-automation.py      # Linux server patch management
│   ├── master-orchestrator.ps1        # Main orchestration script
│   └── vulnerability-dashboard.html    # Web-based monitoring dashboard
├── config/
│   └── automation-config.json         # Main configuration file
├── reports/                           # Generated reports directory
├── logs/                             # Log files directory
└── setup-automation.ps1              # Initial setup script
```

## 🔧 Components

### Windows Patch Management (Intune)
- **Script**: `automation/intune-patch-policies.ps1`
- **Features**:
  - Creates deployment rings (Pilot, Broad, Critical)
  - Configures Windows Update for Business policies
  - Automated group assignments
  - Maintenance window scheduling

### Linux Server Management
- **Script**: `automation/linux-patch-automation.py`
- **Supported Distributions**: Ubuntu, Debian, RHEL, CentOS, Fedora
- **Features**:
  - Automated security update detection
  - Package manager integration
  - Reboot management
  - Centralized reporting

### Vulnerability Assessment
- **Script**: `automation/vulnerability-assessment.ps1`
- **Features**:
  - Microsoft Defender integration
  - Risk-based prioritization
  - Compliance tracking
  - HTML and CSV reporting

### Monitoring Dashboard
- **File**: `automation/vulnerability-dashboard.html`
- **Features**:
  - Real-time vulnerability metrics
  - Interactive charts and graphs
  - Device status overview
  - One-click patch deployment

## ⚙️ Configuration Options

### Deployment Rings
- **Pilot Ring**: Immediate deployment (0-day deferral)
- **Broad Ring**: Standard deployment (7-day deferral)
- **Critical Ring**: Extended testing (14-day deferral)

### Maintenance Windows
- Configurable per environment
- Automatic reboot scheduling
- Rollback capabilities

### Notifications
- Email alerts
- Microsoft Teams integration
- Webhook notifications
- Custom reporting

## 🔐 Security Best Practices

### Authentication
- Uses Microsoft Graph with least-privilege permissions
- Service principal authentication supported
- SSH key-based authentication for Linux servers

### Risk Management
- CVSS-based vulnerability prioritization
- Automated rollback on failure
- Compliance threshold monitoring
- Change management integration

## 📊 Automation Workflows

### Daily Operations
1. **01:00 AM** - Vulnerability scan execution
2. **02:00 AM** - Patch deployment (maintenance window)
3. **08:00 AM** - Daily report generation
4. **Continuous** - Real-time monitoring

### Weekly Operations
1. **Sunday 02:00 AM** - Full patch deployment cycle
2. **Monday 09:00 AM** - Weekly summary report
3. **Weekly** - Compliance review and adjustment

## 🚨 Emergency Response

### Critical Vulnerability Response
```powershell
# Immediate patch deployment for critical vulnerabilities
.\automation\master-orchestrator.ps1 -TenantId "your-tenant-id" -Emergency
```

### Rollback Procedures
```powershell
# Rollback recent changes if issues occur
.\automation\rollback-patches.ps1 -TenantId "your-tenant-id" -Hours 24
```

## 📈 Monitoring and Reporting

### Dashboard Features
- Real-time vulnerability counts
- Patch compliance percentages
- Platform distribution charts
- Recent activity timeline
- One-click remediation actions

### Report Types
- **Daily**: Vulnerability status and patch compliance
- **Weekly**: Comprehensive security posture summary
- **Monthly**: Trend analysis and risk assessment
- **Ad-hoc**: On-demand vulnerability assessments

## 🔧 Customization

### Adding New Platforms
1. Create platform-specific script in `automation/` directory
2. Update `master-orchestrator.ps1` to include new platform
3. Add configuration section to `automation-config.json`

### Custom Notifications
1. Modify notification functions in scripts
2. Add webhook endpoints to configuration
3. Implement custom reporting templates

## 📋 Prerequisites

### PowerShell Modules
- Microsoft.Graph.Authentication
- Microsoft.Graph.DeviceManagement
- Microsoft.Graph.Security
- Az.Accounts
- Az.Automation

### Permissions Required
- **Intune**: DeviceManagementConfiguration.ReadWrite.All
- **Azure**: Automation Contributor
- **Microsoft Graph**: SecurityEvents.Read.All

### Linux Requirements
- Python 3.6+
- SSH access to remote servers
- sudo privileges for package management

## 🆘 Troubleshooting

### Common Issues
1. **Authentication Failures**: Verify tenant ID and permissions
2. **Module Import Errors**: Run setup script with -InstallModules
3. **Linux SSH Issues**: Check SSH key configuration and connectivity
4. **Report Generation Failures**: Verify output directory permissions

### Debug Mode
```powershell
# Enable verbose logging for troubleshooting
.\automation\master-orchestrator.ps1 -TenantId "your-tenant-id" -Verbose -Debug
```

## 📞 Support

For issues or questions:
1. Check the logs in the `logs/` directory
2. Review configuration in `config/automation-config.json`
3. Test connectivity with individual scripts
4. Consult Microsoft documentation for Graph API issues

---

**⚠️ Important**: Always test in a non-production environment first and ensure you have proper backups and rollback procedures in place.