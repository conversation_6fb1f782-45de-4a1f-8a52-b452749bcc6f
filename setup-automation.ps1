# Vulnerability Management Automation Setup Script
param([switch]$InstallModules)

Write-Host "=== Vulnerability Management Automation Setup ===" -ForegroundColor Cyan

# Create directories
Write-Host "Creating directories..." -ForegroundColor Yellow
$dirs = @("automation", "config", "reports", "logs")
foreach ($dir in $dirs) {
    if (!(Test-Path $dir)) {
        New-Item -Path $dir -ItemType Directory -Force | Out-Null
        Write-Host "✓ Created: $dir" -ForegroundColor Green
    } else {
        Write-Host "✓ Exists: $dir" -ForegroundColor Green
    }
}

# Install modules
if ($InstallModules) {
    Write-Host "Installing PowerShell modules..." -ForegroundColor Yellow
    $modules = @("Microsoft.Graph.Authentication", "Microsoft.Graph.DeviceManagement", "Microsoft.Graph.Security")
    foreach ($module in $modules) {
        try {
            Install-Module -Name $module -Force -AllowClobber -Scope CurrentUser
            Write-Host "✓ Installed: $module" -ForegroundColor Green
        } catch {
            Write-Warning "Failed to install $module"
        }
    }
}

# Test config
if (Test-Path "config\automation-config.json") {
    Write-Host "✓ Configuration file found" -ForegroundColor Green
} else {
    Write-Warning "Configuration file missing"
}

Write-Host "
Setup completed!" -ForegroundColor Green
Write-Host "Next: Edit config\automation-config.json with your details" -ForegroundColor Yellow