# Vulnerability Management Automation Setup Script
# Installs required modules and configures the automation environment

param(
    [Parameter(Mandatory=$false)]
    [switch]$InstallModules,

    [Parameter(Mandatory=$false)]
    [switch]$ConfigureScheduledTasks,

    [Parameter(Mandatory=$false)]
    [string]$TenantId
)

Write-Host "=== Vulnerability Management Automation Setup ===" -ForegroundColor Cyan

# Function to install required PowerShell modules
function Install-RequiredModules {
    Write-Host "Installing required PowerShell modules..." -ForegroundColor Yellow

    $modules = @(
        "Microsoft.Graph.Authentication",
        "Microsoft.Graph.DeviceManagement",
        "Microsoft.Graph.Security",
        "Microsoft.Graph.Groups",
        "Az.Accounts",
        "Az.Automation",
        "Az.Resources"
    )

    foreach ($module in $modules) {
        Write-Host "Installing $module..." -ForegroundColor White
        try {
            Install-Module -Name $module -Force -AllowClobber -Scope CurrentUser
            Write-Host "✓ $module installed successfully" -ForegroundColor Green
        }
        catch {
            Write-Warning "Failed to install $module : $($_.Exception.Message)"
        }
    }
}

# Function to create scheduled tasks
function New-AutomationScheduledTasks {
    Write-Host "Creating scheduled tasks for automation..." -ForegroundColor Yellow

    # Daily vulnerability assessment task
    $vulnAssessmentAction = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-ExecutionPolicy Bypass -File `"$PWD\automation\vulnerability-assessment.ps1`" -TenantId $TenantId"
    $vulnAssessmentTrigger = New-ScheduledTaskTrigger -Daily -At "01:00AM"
    $vulnAssessmentSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

    try {
        Register-ScheduledTask -TaskName "VulnerabilityAssessment-Daily" -Action $vulnAssessmentAction -Trigger $vulnAssessmentTrigger -Settings $vulnAssessmentSettings -Description "Daily vulnerability assessment and reporting" -Force
        Write-Host "✓ Daily vulnerability assessment task created" -ForegroundColor Green
    }
    catch {
        Write-Warning "Failed to create vulnerability assessment task: $($_.Exception.Message)"
    }

    # Weekly patch deployment task
    $patchDeploymentAction = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-ExecutionPolicy Bypass -File `"$PWD\automation\master-orchestrator.ps1`" -TenantId $TenantId"
    $patchDeploymentTrigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Sunday -At "02:00AM"
    $patchDeploymentSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

    try {
        Register-ScheduledTask -TaskName "PatchDeployment-Weekly" -Action $patchDeploymentAction -Trigger $patchDeploymentTrigger -Settings $patchDeploymentSettings -Description "Weekly automated patch deployment" -Force
        Write-Host "✓ Weekly patch deployment task created" -ForegroundColor Green
    }
    catch {
        Write-Warning "Failed to create patch deployment task: $($_.Exception.Message)"
    }
}

# Function to validate configuration
function Test-AutomationConfiguration {
    Write-Host "Validating automation configuration..." -ForegroundColor Yellow

    $configPath = ".\config\automation-config.json"
    if (!(Test-Path $configPath)) {
        Write-Error "Configuration file not found: $configPath"
        return $false
    }

    try {
        $config = Get-Content $configPath | ConvertFrom-Json
        Write-Host "✓ Configuration file is valid JSON" -ForegroundColor Green

        # Validate required sections
        $requiredSections = @("General", "Intune", "LinuxServers", "Notifications")
        foreach ($section in $requiredSections) {
            if ($config.PSObject.Properties.Name -contains $section) {
                Write-Host "✓ $section configuration found" -ForegroundColor Green
            } else {
                Write-Warning "Missing configuration section: $section"
            }
        }

        return $true
    }
    catch {
        Write-Error "Invalid configuration file: $($_.Exception.Message)"
        return $false
    }
}

# Function to create directory structure
function Initialize-DirectoryStructure {
    Write-Host "Creating directory structure..." -ForegroundColor Yellow

    $directories = @("automation", "config", "reports", "logs")

    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -Path $dir -ItemType Directory -Force | Out-Null
            Write-Host "✓ Created directory: $dir" -ForegroundColor Green
        } else {
            Write-Host "✓ Directory exists: $dir" -ForegroundColor Green
        }
    }
}

# Function to test connectivity
function Test-ConnectivityRequirements {
    Write-Host "Testing connectivity requirements..." -ForegroundColor Yellow

    # Test Microsoft Graph connectivity
    try {
        $graphTest = Invoke-WebRequest -Uri "https://graph.microsoft.com/v1.0/" -UseBasicParsing -TimeoutSec 10
        if ($graphTest.StatusCode -eq 200) {
            Write-Host "✓ Microsoft Graph connectivity: OK" -ForegroundColor Green
        }
    }
    catch {
        Write-Warning "Microsoft Graph connectivity test failed"
    }

    # Test Azure connectivity
    try {
        $azureTest = Invoke-WebRequest -Uri "https://management.azure.com/" -UseBasicParsing -TimeoutSec 10
        if ($azureTest.StatusCode -eq 401) {  # 401 is expected without auth
            Write-Host "✓ Azure Management API connectivity: OK" -ForegroundColor Green
        }
    }
    catch {
        Write-Warning "Azure Management API connectivity test failed"
    }
}

# Main execution
Write-Host "Starting setup process..." -ForegroundColor Yellow

# Initialize directory structure
Initialize-DirectoryStructure

# Install modules if requested
if ($InstallModules) {
    Install-RequiredModules
}

# Validate configuration
$configValid = Test-AutomationConfiguration

# Test connectivity
Test-ConnectivityRequirements

# Create scheduled tasks if requested
if ($ConfigureScheduledTasks -and $TenantId) {
    New-AutomationScheduledTasks
}

# Display setup summary
Write-Host "`n=== Setup Summary ===" -ForegroundColor Cyan
Write-Host "Directory structure: ✓ Created" -ForegroundColor Green
Write-Host "Configuration validation: $(if ($configValid) { '✓ Valid' } else { '✗ Invalid' })" -ForegroundColor $(if ($configValid) { 'Green' } else { 'Red' })
Write-Host "PowerShell modules: $(if ($InstallModules) { '✓ Installed' } else { 'Skipped (use -InstallModules)' })" -ForegroundColor $(if ($InstallModules) { 'Green' } else { 'Yellow' })
Write-Host "Scheduled tasks: $(if ($ConfigureScheduledTasks) { '✓ Created' } else { 'Skipped (use -ConfigureScheduledTasks)' })" -ForegroundColor $(if ($ConfigureScheduledTasks) { 'Green' } else { 'Yellow' })

Write-Host "`n=== Next Steps ===" -ForegroundColor Yellow
Write-Host "1. Update config\automation-config.json with your environment details" -ForegroundColor White
Write-Host "2. Run: .\setup-automation.ps1 -InstallModules -ConfigureScheduledTasks -TenantId 'your-tenant-id'" -ForegroundColor White
Write-Host "3. Test with: .\automation\master-orchestrator.ps1 -TenantId 'your-tenant-id' -DryRun" -ForegroundColor White
Write-Host "4. Open vulnerability-dashboard.html in your browser for monitoring" -ForegroundColor White

Write-Host "`nSetup completed!" -ForegroundColor Green