﻿# Simple Setup Script
Write-Host "=== Vulnerability Management Setup ===" -ForegroundColor Cyan

# Create directories
$directories = @("automation", "config", "reports", "logs")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -Path $dir -ItemType Directory -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "Directory exists: $dir" -ForegroundColor Green
    }
}

# Check configuration
if (Test-Path "config\automation-config.json") {
    Write-Host "Configuration file found" -ForegroundColor Green
} else {
    Write-Host "Configuration file missing - please edit config\automation-config.json" -ForegroundColor Yellow
}

Write-Host "`nSetup completed!" -ForegroundColor Green
Write-Host "To install modules, run: Install-Module Microsoft.Graph.Authentication -Force" -ForegroundColor Yellow
