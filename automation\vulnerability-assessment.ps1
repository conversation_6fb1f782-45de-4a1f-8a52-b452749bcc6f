# Comprehensive Vulnerability Assessment and Reporting Script
# Integrates with Microsoft Defender, Intune, and generates automated reports

param(
    [Parameter(Mandatory=$true)]
    [string]$TenantId,

    [Parameter(Mandatory=$false)]
    [string]$OutputPath = ".\reports",

    [Parameter(Mandatory=$false)]
    [switch]$SendEmail,

    [Parameter(Mandatory=$false)]
    [string]$EmailRecipients
)

# Import required modules
Import-Module Microsoft.Graph.Authentication
Import-Module Microsoft.Graph.Security
Import-Module Microsoft.Graph.DeviceManagement

# Connect to Microsoft Graph
Write-Host "Connecting to Microsoft Graph..." -ForegroundColor Green
Connect-MgGraph -TenantId $TenantId -Scopes @(
    "SecurityEvents.Read.All",
    "DeviceManagementManagedDevices.Read.All",
    "DeviceManagementConfiguration.Read.All",
    "Mail.Send"
)

# Create output directory
if (!(Test-Path $OutputPath)) {
    New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null
}

# Function to get vulnerability data from Defender
function Get-DefenderVulnerabilities {
    Write-Host "Fetching vulnerability data from Microsoft Defender..." -ForegroundColor Yellow

    try {
        # Get security recommendations
        $recommendations = Invoke-MgGraphRequest -Method GET -Uri "https://graph.microsoft.com/v1.0/security/secureScores" | Select-Object -ExpandProperty value

        # Get device compliance data
        $devices = Get-MgDeviceManagementManagedDevice -All

        $vulnerabilityData = @()

        foreach ($device in $devices) {
            $deviceVulns = @{
                DeviceName = $device.DeviceName
                Platform = $device.OperatingSystem
                ComplianceState = $device.ComplianceState
                LastSyncDateTime = $device.LastSyncDateTime
                OSVersion = $device.OSVersion
                Vulnerabilities = @()
            }

            # Add device to vulnerability data
            $vulnerabilityData += $deviceVulns
        }

        return $vulnerabilityData
    }
    catch {
        Write-Error "Failed to fetch vulnerability data: $($_.Exception.Message)"
        return @()
    }
}

# Function to get patch compliance status
function Get-PatchComplianceStatus {
    Write-Host "Analyzing patch compliance status..." -ForegroundColor Yellow

    try {
        # Get update compliance data
        $updateCompliance = Invoke-MgGraphRequest -Method GET -Uri "https://graph.microsoft.com/v1.0/deviceManagement/reports/getCompliancePolicyNonComplianceReport" -Body @{
            reportName = "CompliancePolicyNonComplianceReport"
            format = "json"
        }

        return $updateCompliance
    }
    catch {
        Write-Error "Failed to get patch compliance status: $($_.Exception.Message)"
        return @()
    }
}

# Function to prioritize vulnerabilities
function Get-VulnerabilityPriority {
    param(
        [array]$Vulnerabilities
    )

    $prioritized = $Vulnerabilities | ForEach-Object {
        $priority = "Low"
        $urgency = "Standard"

        # Determine priority based on CVSS score and exploitability
        if ($_.Severity -eq "Critical") {
            $priority = "Critical"
            $urgency = "Immediate"
        }
        elseif ($_.Severity -eq "High") {
            $priority = "High"
            $urgency = "Within 7 days"
        }
        elseif ($_.Severity -eq "Medium") {
            $priority = "Medium"
            $urgency = "Within 30 days"
        }

        # Increase priority for internet-facing systems
        if ($_.IsInternetFacing) {
            if ($priority -eq "Medium") { $priority = "High" }
            if ($priority -eq "Low") { $priority = "Medium" }
        }

        $_ | Add-Member -NotePropertyName "Priority" -NotePropertyValue $priority -Force
        $_ | Add-Member -NotePropertyName "Urgency" -NotePropertyValue $urgency -Force

        return $_
    }

    return $prioritized | Sort-Object @{Expression="Priority"; Descending=$true}, @{Expression="Severity"; Descending=$true}
}

# Function to generate HTML report
function New-VulnerabilityReport {
    param(
        [array]$VulnerabilityData,
        [string]$ReportPath
    )

    $reportDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $totalDevices = $VulnerabilityData.Count
    $criticalVulns = ($VulnerabilityData | Where-Object { $_.Priority -eq "Critical" }).Count
    $highVulns = ($VulnerabilityData | Where-Object { $_.Priority -eq "High" }).Count

    $htmlContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>Vulnerability Assessment Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .summary { display: flex; justify-content: space-around; margin: 20px 0; }
        .metric { text-align: center; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .critical { background-color: #ffebee; border-color: #f44336; }
        .high { background-color: #fff3e0; border-color: #ff9800; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Vulnerability Assessment Report</h1>
        <p>Generated: $reportDate</p>
        <p>Total Devices: $totalDevices</p>
    </div>

    <div class="summary">
        <div class="metric critical">
            <h3>$criticalVulns</h3>
            <p>Critical</p>
        </div>
        <div class="metric high">
            <h3>$highVulns</h3>
            <p>High Priority</p>
        </div>
    </div>

    <table>
        <tr><th>Device</th><th>Platform</th><th>Compliance</th><th>Priority</th></tr>
"@

    foreach ($device in $VulnerabilityData) {
        $htmlContent += "<tr><td>$($device.DeviceName)</td><td>$($device.Platform)</td><td>$($device.ComplianceState)</td><td>$($device.Priority)</td></tr>"
    }

    $htmlContent += "</table></body></html>"
    $htmlContent | Out-File -FilePath $ReportPath -Encoding UTF8
    Write-Host "Report generated: $ReportPath" -ForegroundColor Green
}

# Main execution
Write-Host "Starting Vulnerability Assessment..." -ForegroundColor Yellow

$vulnerabilities = Get-DefenderVulnerabilities
$prioritizedVulns = Get-VulnerabilityPriority -Vulnerabilities $vulnerabilities

$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$htmlReportPath = Join-Path $OutputPath "vulnerability-report-$timestamp.html"

New-VulnerabilityReport -VulnerabilityData $prioritizedVulns -ReportPath $htmlReportPath

Write-Host "Assessment completed! Report: $htmlReportPath" -ForegroundColor Green
Disconnect-MgGraph