#!/usr/bin/env python3
"""
Linux Server Patch Automation Script
Supports Ubuntu/Debian, RHEL/CentOS, and SUSE systems
Integrates with centralized reporting
"""

import subprocess
import json
import datetime
import os
import sys
import logging
import requests
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/patch-automation.log'),
        logging.StreamHandler()
    ]
)

class LinuxPatchManager:
    def __init__(self, config_file: str = '/etc/patch-automation.conf'):
        self.config = self.load_config(config_file)
        self.hostname = subprocess.check_output(['hostname']).decode().strip()
        self.distro = self.detect_distribution()

    def load_config(self, config_file: str) -> Dict:
        """Load configuration from file"""
        default_config = {
            "maintenance_window": "02:00-04:00",
            "auto_reboot": False,
            "notification_webhook": None,
            "exclude_packages": [],
            "test_mode": False
        }

        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
            except Exception as e:
                logging.warning(f"Failed to load config: {e}")

        return default_config

    def detect_distribution(self) -> str:
        """Detect Linux distribution"""
        try:
            with open('/etc/os-release', 'r') as f:
                for line in f:
                    if line.startswith('ID='):
                        return line.split('=')[1].strip().strip('"')
        except:
            return "unknown"

    def check_available_updates(self) -> List[Dict]:
        """Check for available security updates"""
        updates = []

        try:
            if self.distro in ['ubuntu', 'debian']:
                # Update package lists
                subprocess.run(['apt', 'update'], check=True, capture_output=True)

                # Get security updates
                result = subprocess.run(
                    ['apt', 'list', '--upgradable'],
                    capture_output=True, text=True, check=True
                )

                for line in result.stdout.split('\n'):
                    if 'security' in line.lower():
                        parts = line.split('/')
                        if len(parts) > 0:
                            package_name = parts[0]
                            updates.append({
                                'package': package_name,
                                'type': 'security',
                                'severity': 'high' if 'critical' in line.lower() else 'medium'
                            })

            elif self.distro in ['rhel', 'centos', 'fedora']:
                # Check for security updates using yum/dnf
                cmd = 'dnf' if os.path.exists('/usr/bin/dnf') else 'yum'
                result = subprocess.run(
                    [cmd, 'updateinfo', 'list', 'security'],
                    capture_output=True, text=True, check=True
                )

                for line in result.stdout.split('\n'):
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 3:
                            updates.append({
                                'package': parts[2] if len(parts) > 2 else 'unknown',
                                'type': 'security',
                                'severity': 'critical' if 'critical' in line.lower() else 'high'
                            })

        except subprocess.CalledProcessError as e:
            logging.error(f"Failed to check updates: {e}")

        return updates

    def install_security_updates(self) -> Dict:
        """Install security updates"""
        result = {
            'success': False,
            'updates_installed': [],
            'errors': [],
            'reboot_required': False
        }

        if self.config.get('test_mode', False):
            logging.info("Running in test mode - no actual updates will be installed")
            return result

        try:
            if self.distro in ['ubuntu', 'debian']:
                # Install security updates
                cmd_result = subprocess.run(
                    ['apt', 'upgrade', '-y', '--only-upgrade'],
                    capture_output=True, text=True, check=True
                )

                # Check if reboot is required
                if os.path.exists('/var/run/reboot-required'):
                    result['reboot_required'] = True

            elif self.distro in ['rhel', 'centos', 'fedora']:
                cmd = 'dnf' if os.path.exists('/usr/bin/dnf') else 'yum'
                cmd_result = subprocess.run(
                    [cmd, 'update', '-y', '--security'],
                    capture_output=True, text=True, check=True
                )

                # Check if reboot is required (simplified check)
                kernel_check = subprocess.run(
                    ['needs-restarting', '-r'],
                    capture_output=True, text=True
                )
                if kernel_check.returncode == 1:
                    result['reboot_required'] = True

            result['success'] = True
            logging.info("Security updates installed successfully")

        except subprocess.CalledProcessError as e:
            error_msg = f"Failed to install updates: {e}"
            result['errors'].append(error_msg)
            logging.error(error_msg)

        return result

    def generate_report(self, updates: List[Dict], install_result: Dict) -> Dict:
        """Generate vulnerability report"""
        report = {
            'hostname': self.hostname,
            'distribution': self.distro,
            'timestamp': datetime.datetime.now().isoformat(),
            'available_updates': len(updates),
            'critical_updates': len([u for u in updates if u.get('severity') == 'critical']),
            'high_updates': len([u for u in updates if u.get('severity') == 'high']),
            'installation_success': install_result.get('success', False),
            'reboot_required': install_result.get('reboot_required', False),
            'errors': install_result.get('errors', []),
            'updates_detail': updates
        }

        return report

    def send_notification(self, report: Dict):
        """Send notification to webhook or email"""
        webhook_url = self.config.get('notification_webhook')

        if webhook_url:
            try:
                response = requests.post(webhook_url, json=report, timeout=30)
                response.raise_for_status()
                logging.info("Notification sent successfully")
            except Exception as e:
                logging.error(f"Failed to send notification: {e}")

    def run_patch_cycle(self):
        """Execute complete patch management cycle"""
        logging.info(f"Starting patch cycle for {self.hostname}")

        # Check for available updates
        updates = self.check_available_updates()
        logging.info(f"Found {len(updates)} security updates")

        # Install updates if any are available
        install_result = {'success': True, 'errors': [], 'reboot_required': False}
        if updates:
            install_result = self.install_security_updates()

        # Generate report
        report = self.generate_report(updates, install_result)

        # Save local report
        report_file = f"/var/log/patch-report-{datetime.datetime.now().strftime('%Y%m%d-%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        # Send notification
        self.send_notification(report)

        # Handle reboot if required and configured
        if install_result.get('reboot_required') and self.config.get('auto_reboot'):
            logging.info("Reboot required - scheduling reboot in 5 minutes")
            subprocess.run(['shutdown', '-r', '+5', 'Automated security update reboot'])

        logging.info("Patch cycle completed")
        return report

def main():
    """Main execution function"""
    if len(sys.argv) > 1 and sys.argv[1] == '--check-only':
        # Only check for updates, don't install
        patch_manager = LinuxPatchManager()
        updates = patch_manager.check_available_updates()
        print(json.dumps(updates, indent=2))
    else:
        # Full patch cycle
        patch_manager = LinuxPatchManager()
        report = patch_manager.run_patch_cycle()
        print(json.dumps(report, indent=2))

if __name__ == "__main__":
    main()