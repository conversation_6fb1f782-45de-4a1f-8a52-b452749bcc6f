# Intune Automated Patch Management Configuration
# This script creates and configures Windows Update policies in Intune

# Requires Microsoft.Graph.Intune module
# Install-Module Microsoft.Graph.Intune -Force

param(
    [Parameter(Mandatory=$true)]
    [string]$TenantId,

    [Parameter(Mandatory=$false)]
    [string]$ClientId,

    [Parameter(Mandatory=$false)]
    [switch]$CreateTestPolicies
)

# Import required modules
Import-Module Microsoft.Graph.Authentication
Import-Module Microsoft.Graph.DeviceManagement

# Connect to Microsoft Graph
Write-Host "Connecting to Microsoft Graph..." -ForegroundColor Green
if ($ClientId) {
    Connect-MgGraph -TenantId $TenantId -ClientId $ClientId -Scopes "DeviceManagementConfiguration.ReadWrite.All"
} else {
    Connect-MgGraph -TenantId $TenantId -Scopes "DeviceManagementConfiguration.ReadWrite.All"
}

# Function to create Windows Update policy
function New-WindowsUpdatePolicy {
    param(
        [string]$PolicyName,
        [string]$Description,
        [string]$DeploymentRing,
        [int]$DeferQualityUpdatesDays = 0,
        [int]$DeferFeatureUpdatesDays = 0,
        [string]$MaintenanceWindow = "02:00"
    )

    $policyBody = @{
        "@odata.type" = "#microsoft.graph.windowsUpdateForBusinessConfiguration"
        displayName = $PolicyName
        description = $Description
        deliveryOptimizationMode = "httpWithPeeringNat"
        prereleaseFeatures = "settingsOnly"
        automaticUpdateMode = "autoInstallAtMaintenanceTime"
        microsoftUpdateServiceAllowed = $true
        driversExcluded = $false
        qualityUpdatesDeferralPeriodInDays = $DeferQualityUpdatesDays
        featureUpdatesDeferralPeriodInDays = $DeferFeatureUpdatesDays
        qualityUpdatesWillBeRolledBack = $false
        featureUpdatesWillBeRolledBack = $false
        businessReadyUpdatesOnly = "businessReadyOnly"
        skipChecksBeforeRestart = $false
        updateWeeks = $null
        qualityUpdatesPaused = $false
        featureUpdatesPaused = $false
        deadlineForFeatureUpdatesInDays = 7
        deadlineForQualityUpdatesInDays = 3
        deadlineGracePeriodInDays = 2
        postponeRebootUntilAfterDeadline = $false
        autoRestartNotificationDismissal = "automatic"
        scheduleRestartWarningInHours = 4
        scheduleImminentRestartWarningInMinutes = 15
        userPauseAccess = "disabled"
        userWindowsUpdateScanAccess = "enabled"
        updateNotificationLevel = "defaultNotifications"
        installationSchedule = @{
            "@odata.type" = "#microsoft.graph.windowsUpdateScheduledInstall"
            scheduledInstallDay = "everyday"
            scheduledInstallTime = $MaintenanceWindow
        }
    }

    try {
        $policy = New-MgDeviceManagementDeviceConfiguration -BodyParameter $policyBody
        Write-Host "Created policy: $PolicyName (ID: $($policy.Id))" -ForegroundColor Green
        return $policy
    }
    catch {
        Write-Error "Failed to create policy $PolicyName : $($_.Exception.Message)"
        return $null
    }
}

# Function to create device group
function New-DeviceGroup {
    param(
        [string]$GroupName,
        [string]$Description,
        [string]$MembershipRule
    )

    $groupBody = @{
        displayName = $GroupName
        description = $Description
        groupTypes = @("DynamicMembership")
        membershipRule = $MembershipRule
        membershipRuleProcessingState = "On"
        mailEnabled = $false
        securityEnabled = $true
    }

    try {
        $group = New-MgGroup -BodyParameter $groupBody
        Write-Host "Created group: $GroupName (ID: $($group.Id))" -ForegroundColor Green
        return $group
    }
    catch {
        Write-Error "Failed to create group $GroupName : $($_.Exception.Message)"
        return $null
    }
}

# Function to assign policy to group
function Set-PolicyAssignment {
    param(
        [string]$PolicyId,
        [string]$GroupId,
        [string]$AssignmentName
    )

    $assignmentBody = @{
        assignments = @(
            @{
                target = @{
                    "@odata.type" = "#microsoft.graph.groupAssignmentTarget"
                    groupId = $GroupId
                }
            }
        )
    }

    try {
        $assignment = Invoke-MgGraphRequest -Method POST -Uri "https://graph.microsoft.com/v1.0/deviceManagement/deviceConfigurations/$PolicyId/assign" -Body ($assignmentBody | ConvertTo-Json -Depth 10)
        Write-Host "Assigned policy to group: $AssignmentName" -ForegroundColor Green
        return $assignment
    }
    catch {
        Write-Error "Failed to assign policy: $($_.Exception.Message)"
        return $null
    }
}

# Main execution
Write-Host "Starting Intune Patch Policy Configuration..." -ForegroundColor Yellow

# Create deployment rings (device groups)
$pilotGroup = New-DeviceGroup -GroupName "Patch-Pilot-Ring" -Description "Pilot devices for early patch deployment" -MembershipRule "(device.deviceOwnership -eq `"Corporate`") and (device.enrollmentProfileName -contains `"Pilot`")"

$broadGroup = New-DeviceGroup -GroupName "Patch-Broad-Ring" -Description "Broad deployment ring for standard patch deployment" -MembershipRule "(device.deviceOwnership -eq `"Corporate`") and (device.enrollmentProfileName -notContains `"Pilot`") and (device.enrollmentProfileName -notContains `"Critical`")"

$criticalGroup = New-DeviceGroup -GroupName "Patch-Critical-Ring" -Description "Critical systems with extended testing period" -MembershipRule "(device.deviceOwnership -eq `"Corporate`") and (device.enrollmentProfileName -contains `"Critical`")"

Start-Sleep -Seconds 10  # Allow time for group creation

# Create patch policies for different rings
if ($pilotGroup) {
    $pilotPolicy = New-WindowsUpdatePolicy -PolicyName "Windows Updates - Pilot Ring" -Description "Immediate deployment for pilot devices" -DeploymentRing "Pilot" -DeferQualityUpdatesDays 0 -DeferFeatureUpdatesDays 0 -MaintenanceWindow "02:00"

    if ($pilotPolicy) {
        Set-PolicyAssignment -PolicyId $pilotPolicy.Id -GroupId $pilotGroup.Id -AssignmentName "Pilot Ring Assignment"
    }
}

if ($broadGroup) {
    $broadPolicy = New-WindowsUpdatePolicy -PolicyName "Windows Updates - Broad Ring" -Description "Standard deployment with 7-day deferral" -DeploymentRing "Broad" -DeferQualityUpdatesDays 7 -DeferFeatureUpdatesDays 30 -MaintenanceWindow "02:00"

    if ($broadPolicy) {
        Set-PolicyAssignment -PolicyId $broadPolicy.Id -GroupId $broadGroup.Id -AssignmentName "Broad Ring Assignment"
    }
}

if ($criticalGroup) {
    $criticalPolicy = New-WindowsUpdatePolicy -PolicyName "Windows Updates - Critical Ring" -Description "Extended testing period for critical systems" -DeploymentRing "Critical" -DeferQualityUpdatesDays 14 -DeferFeatureUpdatesDays 60 -MaintenanceWindow "03:00"

    if ($criticalPolicy) {
        Set-PolicyAssignment -PolicyId $criticalPolicy.Id -GroupId $criticalGroup.Id -AssignmentName "Critical Ring Assignment"
    }
}

Write-Host "Intune patch policy configuration completed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Review created policies in Intune admin center" -ForegroundColor White
Write-Host "2. Adjust device group membership rules as needed" -ForegroundColor White
Write-Host "3. Monitor deployment progress in Defender vulnerability dashboard" -ForegroundColor White

# Disconnect from Graph
Disconnect-MgGraph